import { faker } from '@faker-js/faker';
import { cleanup, fireEvent, screen } from '@testing-library/react';
import { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeMTGFilter } from '../../../../tests/fake/FakeFilters';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { FilterComponentType } from '../../../models/FilterComponent';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { Game } from '../../../models/Game';
import { ComposableFilter } from './ComposableFilter';

const mockOnUpdate = vi.fn();

const createMockFilter = () => create(FakeMTGFilter, { empty: true });

const GAMES = [Game.MTG, Game.POKEMON, Game.YUGIOH, Game.LORCANA];

const DEFAULT_PROPS = {
  filter: createMockFilter(),
  game: Game.MTG,
  type: FilterComponentType.QUERY,
  displayType: FilterDisplay.PERMANENT,
  onUpdate: mockOnUpdate,
};

const renderComposableFilter = (props: Partial<Omit<ComponentProps<typeof ComposableFilter>, 'dispatcher'>> = {}) => {
  return renderWithDispatcher(ComposableFilter, { ...DEFAULT_PROPS, ...props });
};

describe('ComposableFilter', () => {
  it('should render', () => {
    renderComposableFilter();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  test('triggers onUpdate prop', () => {
    GAMES.forEach((game) => {
      renderComposableFilter({
        game,
        filter: createMockFilter(),
        type: FilterComponentType.QUERY,
      });

      const textInput = screen.getByRole('textbox');
      fireEvent.change(textInput, { target: { value: faker.lorem.words(2) } });

      expect(mockOnUpdate).toHaveBeenCalled();
      cleanup();
    });
  });

  it('render nothing for invalid game', () => {
    const { container } = renderComposableFilter({
      game: faker.lorem.word() as Game,
    });

    expect(container.firstChild).toBeNull();
  });
});

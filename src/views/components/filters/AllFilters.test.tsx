import { faker } from '@faker-js/faker';
import { fireEvent, screen } from '@testing-library/react';
import { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { create, createArray } from '../../../../tests/fake/Fake';
import { FakeCardList } from '../../../../tests/fake/FakeCardList';
import { FakeMTGFilter } from '../../../../tests/fake/FakeFilters';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as FilterActions from '../../../actions/FilterActions';
import { AllFilters } from './AllFilters';

vi.mock('../../../actions/FilterActions');

const mockFilterActions = vi.mocked(FilterActions);

const DEFAULT_PROPS = {
  mergedFilters: create(FakeMTGFilter, { empty: true }),
  filterOptions: create(FakeMTGFilter),
  advancedFilterOptions: create(FakeMTGFilter),
  cardLists: createArray(FakeCardList, 2),
  cardPage: faker.lorem.word(),
};

const renderAllFilters = (props: Partial<ComponentProps<typeof AllFilters>> = {}) => {
  return renderWithDispatcher(AllFilters, {
    ...DEFAULT_PROPS,
    ...props,
  });
};

describe('AllFilters', () => {
  describe('when rendered', () => {
    it('basic render', () => {
      renderAllFilters();
      expect(screen.getByText('Filters')).toBeInTheDocument();
      expect(screen.queryByText('Collection Filters')).not.toBeInTheDocument();
    });

    test('open FilterDialog', () => {
      renderAllFilters();

      const filtersButton = screen.getByText('Filters');
      fireEvent.click(filtersButton);

      expect(screen.getByText('Collection Filters')).toBeInTheDocument();
    });
  });

  describe('with FilterDialog', () => {
    test('trigger onDismiss', () => {
      renderAllFilters();

      const filtersButton = screen.getByText('Filters');
      fireEvent.click(filtersButton);
      expect(screen.getByText('Collection Filters')).toBeInTheDocument();

      const dismissButton = screen.getByText('Cancel');
      fireEvent.click(dismissButton);

      expect(screen.queryByText('Collection Filters')).not.toBeInTheDocument();
    });

    test('trigger FilterActions.activateFilter', () => {
      renderAllFilters();

      const filtersButton = screen.getByText('Filters');
      fireEvent.click(filtersButton);

      const applyButton = screen.getByText('Apply');
      fireEvent.click(applyButton);

      expect(mockFilterActions.activateFilter).toHaveBeenCalledTimes(1);
    });
  });

  describe('with subscription restrictions', () => {
    describe('when merchant', () => {
      it('allows all filters for merchants', async () => {
        const { setupMerchantSubscription } = renderAllFilters();

        setupMerchantSubscription();

        const filtersButton = screen.getByText('Filters');
        fireEvent.click(filtersButton);

        expect(screen.getByText('Collection Filters')).toBeInTheDocument();
        fireEvent.mouseDown(screen.getByRole('textbox'));
        expect(screen.getByText('Card List')).toBeInTheDocument();
      });
    });
    describe('when not merchant', () => {
      it('restricts CARD_LIST filter', () => {
        renderAllFilters();

        const filtersButton = screen.getByText('Filters');
        fireEvent.click(filtersButton);

        fireEvent.mouseDown(screen.getByRole('textbox'));
        expect(screen.queryByText('Card List')).toBeNull();
      });
    });
  });
});

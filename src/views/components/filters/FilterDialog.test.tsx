import { faker } from '@faker-js/faker';
import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeCardList } from '../../../../tests/fake/FakeCardList';
import { FakeMTGFilter } from '../../../../tests/fake/FakeFilters';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { FilterComponentType } from '../../../models/FilterComponent';
import { FilterDialog } from './FilterDialog';

const mockOnUpdate = vi.fn();
const mockOnDismiss = vi.fn();

const fakeFilter = new FakeMTGFilter();
const fakeCardList = new FakeCardList();

const createCardLists = ({ count = 2 } = {}) => {
  const lists = Array.from({ length: count }, () => {
    const cardList = fakeCardList.fake();
    return [cardList.get('uuid'), cardList] as [string, any];
  });
  return Immutable.OrderedMap<string, any>(lists);
};

const createDisabledFilters = ({ types = [] as FilterComponentType[] } = {}) => {
  return Immutable.List(types);
};

const DEFAULT_PROPS = {
  title: faker.lorem.words(2),
  isOpen: true,
  filters: create(FakeMTGFilter, { empty: true }),
  cardLists: createCardLists(),
  onUpdate: mockOnUpdate,
  onDismiss: mockOnDismiss,
};

const renderFilterDialog = (props: Partial<Omit<ComponentProps<typeof FilterDialog>, 'dispatcher'>> = {}) => {
  return renderWithDispatcher(FilterDialog, {
    ...DEFAULT_PROPS,
    ...props,
  });
};

const clickApply = () => {
  const applyButton = screen.getByText('Apply');
  fireEvent.click(applyButton);
};

describe('FilterDialog', () => {
  describe('when rendered', () => {
    it('basic render', () => {
      const title = faker.lorem.words(3);
      const description = faker.lorem.sentence();
      renderFilterDialog({ title, description });
      expect(screen.getByText(title)).toBeInTheDocument();
      expect(screen.getByText('Add Filter')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
      expect(screen.getByText('Apply')).toBeInTheDocument();
      expect(screen.getByText(description)).toBeInTheDocument();
    });

    it('hides description props when not provided', () => {
      const { container } = renderFilterDialog();
      expect(container.querySelector('.dialog-subheading-xs')).not.toBeInTheDocument();
    });
  });

  describe('with Dialog', () => {
    it('renders when open', () => {
      renderFilterDialog({ isOpen: true });
      expect(screen.getByText('Apply')).toBeInTheDocument();
    });

    it('hides when closed', () => {
      renderFilterDialog({ isOpen: false });
      expect(screen.queryByText('Apply')).not.toBeInTheDocument();
    });

    test('trigger onDismiss', () => {
      const { container } = renderFilterDialog();
      const dismissButton = container.querySelector('.dialog-dismiss');

      fireEvent.click(dismissButton!);
      expect(mockOnDismiss).toHaveBeenCalledTimes(1);
    });

    test('trigger onDismiss when cancel button is clicked', () => {
      renderFilterDialog();
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
      expect(mockOnDismiss).toHaveBeenCalledTimes(1);
    });
  });

  describe('with AddFilterComponent', () => {
    // Attempted to cover `onUpdate` with a test, but it's currently not feasible because
    // `AddFilterComponent` does not utilize `onUpdate` directly. :(
    it('triggers updateLocalFilter state', () => {
      renderFilterDialog();
      fireEvent.mouseDown(screen.getByRole('textbox'));
      fireEvent.click(screen.getByText('Card Name'));

      // The filter should now be visible, proving localFilter state was updated
      expect(screen.getByText('Card Name')).toBeInTheDocument();

      // Apply should pass the updated localFilter to onUpdate
      clickApply();
      expect(mockOnUpdate).toHaveBeenCalled();
    });
  });

  describe('with ComposableFilter', () => {
    it('triggers updateLocalFilter state', () => {
      const initialFilter = fakeFilter
        .fake({ empty: true })
        .set('filterOrder', Immutable.OrderedSet([FilterComponentType.QUERY]))
        .setQuery('initial');

      renderFilterDialog({ filters: initialFilter });

      const queryInput = screen.getByDisplayValue('initial');
      fireEvent.change(queryInput, { target: { value: 'updated query' } });

      clickApply();

      expect(mockOnUpdate).toHaveBeenCalledTimes(1);
    });
  });
});

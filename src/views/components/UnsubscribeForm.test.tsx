import { fireEvent, render, screen, within } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import UnsubscribeForm, { shuffle } from './UnsubscribeForm';

const mockOnComplete = vi.fn();
const mockOnDismiss = vi.fn();

const DEFAULT_PROPS: ComponentProps<typeof UnsubscribeForm> = {
  isOpen: true,
  onComplete: mockOnComplete,
  onDismiss: mockOnDismiss,
};

const renderUnsubscribeForm = (props: Partial<ComponentProps<typeof UnsubscribeForm>> = {}) => {
  return render(<UnsubscribeForm {...DEFAULT_PROPS} {...props} />);
};

it('renders the dialog', () => {
  renderUnsubscribeForm();
  expect(screen.getByRole('heading', { name: "Let us know why you're leaving" })).toBeInTheDocument();
});

it('renders all predefined options', () => {
  renderUnsubscribeForm();

  // Check that some of the predefined options are rendered
  expect(screen.getByText('The subscription fee is too expensive for me.')).toBeInTheDocument();
  expect(screen.getByText("I'm not getting the value I expected.")).toBeInTheDocument();
  expect(screen.getByText('I experienced too many technical issues.')).toBeInTheDocument();
  expect(screen.getByText('I no longer have a need for the platform.')).toBeInTheDocument();
  expect(screen.getByText('I am leaving to use another platform.')).toBeInTheDocument();
});

it('renders the other option with input field', () => {
  renderUnsubscribeForm();
  expect(screen.getByRole('textbox')).toBeInTheDocument();
  expect(screen.getByPlaceholderText('Other...')).toBe(screen.getByRole('textbox'));
});

it('renders the submit button', () => {
  renderUnsubscribeForm();
  expect(screen.getByText('Cancel Subscription')).toBeInTheDocument();
});

it('renders checkboxes for all options', () => {
  renderUnsubscribeForm();
  // Should have 6 checkboxes total (5 predefined + 1 other)
  const checkboxes = screen.getAllByRole('checkbox');
  expect(checkboxes).toHaveLength(6);
});

it('does not render when isOpen is false', () => {
  const { container } = renderUnsubscribeForm({ isOpen: false });
  expect(container).toBeEmptyDOMElement();
});

it('selects predefined option when clicked and deselects others', () => {
  renderUnsubscribeForm();

  const firstOption = screen.getByText('The subscription fee is too expensive for me.');
  const secondOption = screen.getByText("I'm not getting the value I expected.");

  // Click first option
  fireEvent.click(firstOption);

  // First option should be checked
  const firstCheckbox = firstOption.parentElement?.querySelector('.checkbox');
  expect(firstCheckbox).toHaveClass('is-checked');

  // Click second option
  fireEvent.click(secondOption);

  // Second option should be checked, first should not
  const secondCheckbox = secondOption.parentElement?.querySelector('.checkbox');
  expect(secondCheckbox).toHaveClass('is-checked');
});

it('the submit button is enabled when an option is selected', () => {
  renderUnsubscribeForm();

  const option = screen.getByText('The subscription fee is too expensive for me.');
  const submitButton = screen.getByText('Cancel Subscription');

  // Initially button should be disabled (no visual indication in this component)
  fireEvent.click(option);

  // After clicking an option, button should be enabled
  // We can test this by checking if clicking the button calls onComplete
  fireEvent.click(submitButton);
  expect(mockOnComplete).toHaveBeenCalledWith(expect.any(Object), 'The subscription fee is too expensive for me.');
});

describe('Other option', () => {
  it('is selected when clicked', () => {
    renderUnsubscribeForm();

    const otherOption = screen.getByPlaceholderText('Other...').parentElement;
    const otherCheckbox = within(otherOption!).getByRole('checkbox');

    fireEvent.click(otherOption!);

    expect(otherCheckbox.parentElement).toHaveClass('is-checked');
  });

  it('enables submit button user gives an Other reason', () => {
    renderUnsubscribeForm();

    const otherInput = screen.getByPlaceholderText('Other...');
    const submitButton = screen.getByText('Cancel Subscription');

    fireEvent.change(otherInput, { target: { value: 'Custom reason' } });

    // Button should be enabled after entering text
    fireEvent.click(submitButton);
    expect(mockOnComplete).toHaveBeenCalledWith(expect.any(Object), 'Custom reason');
  });

  it('deselects predefined options when other is selected', () => {
    renderUnsubscribeForm();

    // First select a predefined option
    const predefinedOption = screen.getByText('The subscription fee is too expensive for me.');
    fireEvent.click(predefinedOption);

    const predefinedCheckbox = predefinedOption.parentElement?.querySelector('.checkbox');
    expect(predefinedCheckbox).toHaveClass('is-checked');

    // Then click other option
    const otherOption = screen.getByPlaceholderText('Other...').parentElement;
    fireEvent.click(otherOption!);

    // Predefined option should be deselected
    expect(predefinedCheckbox).not.toHaveClass('is-checked');
  });
});

describe('submit button', () => {
  it('does not call onComplete when no option is selected', () => {
    renderUnsubscribeForm();

    const submitButton = screen.getByText('Cancel Subscription');
    fireEvent.click(submitButton);

    expect(mockOnComplete).not.toHaveBeenCalled();
  });

  it('calls onComplete with predefined reason when predefined option is selected', () => {
    renderUnsubscribeForm();

    const option = screen.getByText('I experienced too many technical issues.');
    const submitButton = screen.getByText('Cancel Subscription');

    fireEvent.click(option);
    fireEvent.click(submitButton);

    expect(mockOnComplete).toHaveBeenCalledWith(expect.any(Object), 'I experienced too many technical issues.');
  });

  it('calls onComplete with custom reason when Other option is used', () => {
    renderUnsubscribeForm();

    const otherInput = screen.getByPlaceholderText('Other...');
    const submitButton = screen.getByText('Cancel Subscription');

    fireEvent.change(otherInput, { target: { value: 'My custom reason' } });
    fireEvent.click(submitButton);

    expect(mockOnComplete).toHaveBeenCalledWith(expect.any(Object), 'My custom reason');
  });
});

describe('dialog dismiss', () => {
  it('calls onDismiss when dialog is dismissed', () => {
    const { container } = renderUnsubscribeForm();

    const dismissButton = container.querySelector('.dialog-dismiss');
    expect(dismissButton).toBeInTheDocument();

    fireEvent.click(dismissButton!);
    expect(mockOnDismiss).toHaveBeenCalled();
  });
});

describe('shuffle utility function', () => {
  it('returns an array with the same length', () => {
    const original = [1, 2, 3, 4, 5];
    const shuffled = shuffle([...original]);
    expect(shuffled).toHaveLength(original.length);
  });

  it('contains all original elements', () => {
    const original = [1, 2, 3, 4, 5];
    const shuffled = shuffle([...original]);

    original.forEach((item) => {
      expect(shuffled).toContain(item);
    });
  });
});

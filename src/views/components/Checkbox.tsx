import check from '@iconify/icons-ic/check';
import remove from '@iconify/icons-ic/remove';
import { Icon } from '@iconify/react';
import * as React from 'react';

export interface IProps {
  className?: string;
  checked?: boolean;
  multiple?: boolean;
  disabled?: boolean;
  onChange?: (checked: boolean) => void;
}

interface IState {
  checked?: boolean;
  focused?: boolean;
}

export class Checkbox extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      checked: false,
    };
  }

  public render() {
    // Ignore the state if the component is controlled.
    const checked =
      this.props.checked !== null && this.props.checked !== undefined ? this.props.checked : this.state.checked;
    const inputDisabled = this.props.disabled === undefined ? false : this.props.disabled;
    return (
      <div
        className={
          'checkbox' +
          (checked || this.props.multiple ? ' is-checked' : '') +
          /* v8 ignore next 3 */
          (this.state.focused ? ' is-focused' : '') +
          (this.props.className ? ' ' + this.props.className : '')
        }
        style={inputDisabled ? { cursor: 'default' } : undefined}
        onClick={inputDisabled ? undefined : this.onClick.bind(this)}
      >
        <input
          disabled={inputDisabled}
          type="checkbox"
          style={{ width: '0rem', height: '0rem', opacity: 0, pointerEvents: 'none' }}
        />

        {this.props.multiple ? (
          <Icon height={'18px'} width={'18px'} icon={remove} />
        ) : checked ? (
          <Icon height={'18px'} width={'18px'} icon={check} />
        ) : null}
      </div>
    );
  }

  private onClick(evt: React.SyntheticEvent<HTMLElement>) {
    // evt.preventDefault();
    // evt.stopPropagation();
    this.toggle();
  }

  /* Following methods are not used */
  /* v8 ignore start */
  private onKeyDownEnter(evt: React.KeyboardEvent<HTMLInputElement>) {
    if (evt.keyCode === 13) {
      // WARNING: do not prevent the default event handler for other key codes
      //          otherwise tabbing will stop working on this component
      evt.preventDefault();
      this.toggle();
    }
  }

  private onFocus(evt: React.SyntheticEvent<HTMLInputElement>) {
    // WARNING: do not prevent the default event handler otherwise tabbing
    //          will stop working on this component
    // evt.preventDefault();
    this.setState({
      focused: true,
    });
  }

  private onBlur(evt: React.SyntheticEvent<HTMLInputElement>) {
    // WARNING: do not prevent the default event handler otherwise tabbing
    //          will stop working on this component
    // evt.preventDefault();
    this.setState({
      focused: false,
    });
  }
  /* v8 ignore stop */

  private toggle() {
    if (this.props.onChange) {
      // Propagate the event if the onChange prop is available.
      this.props.onChange(
        this.props.checked !== null && this.props.checked !== undefined ? !this.props.checked : !this.state.checked,
      );
    } else if (this.props.checked === null || this.props.checked === undefined) {
      // Handle the event internally, ignoring the change if the component is controlled.
      this.setState({ checked: !this.state.checked });
    }
  }
}

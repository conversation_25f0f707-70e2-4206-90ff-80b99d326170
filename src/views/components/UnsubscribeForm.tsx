import * as React from 'react';
import { Checkbox } from './Checkbox';
import { Dialog } from './Dialog';

interface IProps {
  isOpen: boolean;
  onComplete: (evt: React.SyntheticEvent<HTMLElement>, reason: string) => void;
  onDismiss: (evt: React.SyntheticEvent<HTMLElement>) => void;
}

interface IState {
  other?: string;
  otherSelected?: boolean;
  selections?: { [position: number]: boolean }; // TODO: Refactor this to just be a single string or something.
  buttonEnabled?: boolean;
}

export default class extends React.Component<IProps, IState> {
  private options = shuffle([
    'The subscription fee is too expensive for me.',
    "I'm not getting the value I expected.",
    'I experienced too many technical issues.',
    'I no longer have a need for the platform.',
    'I am leaving to use another platform.',
  ]);

  constructor(props: IProps) {
    super(props);
    this.state = {
      other: '',
      otherSelected: false,
      selections: {},
      buttonEnabled: false,
    };
  }

  public render() {
    return (
      <Dialog className="unsubscribe-form" isOpen={this.props.isOpen} onDismiss={this.props.onDismiss}>
        <h2 className="unsubscribe-form-heading">Let us know why you're leaving</h2>

        {this.options.map((opt, index) => {
          return (
            <div
              key={'option-' + index}
              className="unsubscribe-form-option"
              onClick={this.onClickOption.bind(this, index)}
            >
              <Checkbox checked={this.state.selections !== undefined ? this.state.selections[index] : false} />
              <span>{opt}</span>
            </div>
          );
        })}

        <div className="unsubscribe-form-option" onClick={this.onClickOtherOption.bind(this)}>
          <Checkbox checked={this.state.otherSelected} />
          <input
            className="input"
            placeholder="Other..."
            value={this.state.other}
            onChange={this.onChangeOther.bind(this)}
          />
        </div>
        <div className="flex justify-center">
          <div className="unsubscribe-form-button button-alert" onClick={this.onClickSubmit.bind(this)}>
            Cancel Subscription
          </div>
        </div>
      </Dialog>
    );
  }

  private onClickOption(index: number, evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();

    const selections: { [position: number]: boolean } = JSON.parse(JSON.stringify(this.state.selections));
    selections[index] = true;
    for (const key in selections) {
      if (key !== index + '') {
        selections[key] = false;
      }
    }

    let isAnySelected = false;

    for (const key in selections) {
      if (selections.hasOwnProperty(key)) {
        isAnySelected = isAnySelected || selections[key];
      }
    }

    this.setState({ selections: selections, buttonEnabled: isAnySelected, otherSelected: false });
  }

  private onClickOtherOption(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();
    const selections: { [position: number]: boolean } = JSON.parse(JSON.stringify(this.state.selections));
    for (const key in selections) {
      selections[key] = false;
    }
    this.setState({
      selections: selections,
      buttonEnabled: !!this.state.other && this.state.other.length > 0,
      otherSelected: true,
    });
  }

  private onChangeOther(evt: React.SyntheticEvent<HTMLElement>) {
    evt.preventDefault();

    const selections: { [position: number]: boolean } = JSON.parse(JSON.stringify(this.state.selections));
    for (const key in selections) {
      selections[key] = false;
    }

    this.setState({
      selections: selections,
      other: (evt.target as HTMLInputElement).value,
      otherSelected: true,
      buttonEnabled: !!(evt.target as HTMLInputElement).value && (evt.target as HTMLInputElement).value.length > 0,
    });
  }

  // TODO: This is horrible. Refactor.
  private onClickSubmit(evt: React.SyntheticEvent<HTMLElement>) {
    if (!this.state.buttonEnabled) {
      return;
    }

    let reason = '';
    if (this.state.other && this.state.other.length) {
      reason = this.state.other;
    } else {
      for (let i = 0; i < this.options.length; i++) {
        if (this.state.selections !== undefined && this.state.selections[i]) {
          reason = this.options[i];
          break;
        }
      }
    }

    this.props.onComplete && this.props.onComplete(evt, reason);
  }
}

export function shuffle(array: Array<any>) {
  let currentIndex = array.length,
    temporaryValue,
    randomIndex;

  // While there remain elements to shuffle...
  while (0 !== currentIndex) {
    // Pick a remaining element...
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex -= 1;

    // And swap it with the current element.
    temporaryValue = array[currentIndex];
    array[currentIndex] = array[randomIndex];
    array[randomIndex] = temporaryValue;
  }

  return array;
}

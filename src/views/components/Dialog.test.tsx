import { fireEvent, render, screen, within } from '@testing-library/react';
import React, { ComponentProps, createRef } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { Dialog, DialogSize } from './Dialog';

const mockOnDismiss = vi.fn();
const mockOnClickBackground = vi.fn();

const DEFAULT_PROPS: ComponentProps<typeof Dialog> = {
  isOpen: true,
  onDismiss: mockOnDismiss,
};

const renderDialog = (props: Partial<ComponentProps<typeof Dialog>> = {}) => {
  return render(
    <Dialog {...DEFAULT_PROPS} {...props}>
      <div>Test Content</div>
    </Dialog>,
  );
};

describe('Dialog', () => {
  describe('when rendered', () => {
    describe('when isOpen is true', () => {
      it('renders the dialog', () => {
        renderDialog();
        expect(screen.getByText('Test Content')).toBeInTheDocument();
      });

      it('renders dialog background', () => {
        const { container } = renderDialog();
        expect(container.querySelector('.dialog-background')).toBeInTheDocument();
      });

      it('renders dismiss button by default', () => {
        const { container } = renderDialog();
        expect(
          within(container.querySelector('.dialog-dismiss') as HTMLElement).getByRole('img', { hidden: true }),
        ).toBeInTheDocument();
      });
    });

    describe('when isOpen is false', () => {
      it('does not render the dialog', () => {
        const { container } = renderDialog({ isOpen: false });
        expect(container).toBeEmptyDOMElement();
      });
    });
  });

  describe('heading prop', () => {
    it('renders heading when provided', () => {
      renderDialog({ heading: 'Test Heading' });
      expect(screen.getByText('Test Heading')).toHaveClass('dialog-heading');
    });

    it('does not render heading when not provided', () => {
      const { container } = renderDialog();
      expect(container.querySelector('.dialog-heading')).not.toBeInTheDocument();
    });
  });

  describe('size prop', () => {
    it('applies small size classes by default', () => {
      const { container } = renderDialog();
      expect(container.querySelector('.dialog')).toHaveClass('col-xs-8', 'col-sm-6', 'col-md-4');
    });

    it('applies small size classes when size is SMALL', () => {
      const { container } = renderDialog({ size: DialogSize.SMALL });
      expect(container.querySelector('.dialog')).toHaveClass('col-xs-8', 'col-sm-6', 'col-md-4');
    });

    it('applies medium size classes when size is MEDIUM', () => {
      const { container } = renderDialog({ size: DialogSize.MEDIUM });
      expect(container.querySelector('.dialog')).toHaveClass('col-xs-10', 'col-sm-8', 'col-md-6');
    });

    it('applies large size classes when size is LARGE', () => {
      const { container } = renderDialog({ size: DialogSize.LARGE });
      expect(container.querySelector('.dialog')).toHaveClass(
        'col-xs-10',
        'col-sm-8',
        'col-md-7',
        'col-lg-7',
        'col-xl-7',
      );
    });

    it('applies x-large size classes when size is X_LARGE', () => {
      const { container } = renderDialog({ size: DialogSize.X_LARGE });
      expect(container.querySelector('.dialog')).toHaveClass(
        'col-xs-11',
        'col-sm-11',
        'col-md-11',
        'col-lg-11',
        'col-xl-11',
      );
    });

    it('applies dynamic width class when size is LARGE_DYNAMIC', () => {
      const { container } = renderDialog({ size: DialogSize.LARGE_DYNAMIC });
      expect(container.querySelector('.dialog')).toHaveClass('dialog-width');
    });
  });

  describe('className prop', () => {
    it('combines custom className with default classes', () => {
      const customClassName = 'custom-class';
      const { container } = renderDialog({ className: customClassName });
      expect(container.querySelector('.dialog')).toHaveClass(
        'dialog',
        customClassName,
        'col-xs-8',
        'col-sm-6',
        'col-md-4',
      );
    });
  });

  describe('dismiss button', () => {
    it('is visible when isClosable is true', () => {
      const { container } = renderDialog({ isClosable: true });
      expect(container.querySelector('.dialog-dismiss')).toBeInTheDocument();
    });

    it('is visible when isDismissible is true', () => {
      const { container } = renderDialog({ isDismissible: true });
      expect(container.querySelector('.dialog-dismiss')).toBeInTheDocument();
    });

    it('is not visible when isClosable is false', () => {
      const { container } = renderDialog({ isClosable: false });
      expect(container.querySelector('.dialog-dismiss')).not.toBeInTheDocument();
    });

    it('is not visible when isDismissible is false', () => {
      const { container } = renderDialog({ isDismissible: false });
      expect(container.querySelector('.dialog-dismiss')).not.toBeInTheDocument();
    });

    it('is not visible when both isClosable and isDismissible are false', () => {
      const { container } = renderDialog({ isClosable: false, isDismissible: false });
      expect(container.querySelector('.dialog-dismiss')).not.toBeInTheDocument();
    });

    it('calls onDismiss when dismiss button is clicked', () => {
      const { container } = renderDialog();

      fireEvent.click(container.querySelector('.dialog-dismiss')!);
      expect(mockOnDismiss).toHaveBeenCalledTimes(1);
    });
  });

  describe('clicking on background', () => {
    it('calls onDismiss', () => {
      const { container } = renderDialog();
      const background = container.querySelector('.dialog-background');

      fireEvent.click(background!);
      expect(mockOnDismiss).toHaveBeenCalledTimes(1);
    });

    it('calls onClickBackground when provided instead of onDismiss', () => {
      const { container } = renderDialog({ onClickBackground: mockOnClickBackground });
      const background = container.querySelector('.dialog-background');

      fireEvent.click(background!);
      expect(mockOnClickBackground).toHaveBeenCalledTimes(1);
      expect(mockOnDismiss).not.toHaveBeenCalled();
    });

    it('does not call onDismiss when isDismissible is false', () => {
      const { container } = renderDialog({ isDismissible: false });
      const background = container.querySelector('.dialog-background');

      fireEvent.click(background!);
      expect(mockOnDismiss).not.toHaveBeenCalled();
    });
  });

  it('does not call onDismiss when clicking on dialog content', () => {
    const { container } = renderDialog();
    const dialogContent = container.querySelector('.dialog');

    fireEvent.click(dialogContent!);
    expect(mockOnDismiss).not.toHaveBeenCalled();
  });

  it('applies ref to dialog background when provided', () => {
    const ref = createRef<HTMLDivElement>();
    renderDialog({ overflowRef: ref });

    expect(ref.current).toBeInstanceOf(HTMLDivElement);
    expect(ref.current).toHaveClass('dialog-background');
  });
});

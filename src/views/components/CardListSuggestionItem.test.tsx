import { faker } from '@faker-js/faker';
import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { CardList, ICardList } from '../../models/CardList';
import { CardListOption } from './CardListSuggestionItem';

// Helper function to create a mock CardList
const createMockCardList = (overrides: Partial<ICardList> = {}) => {
  const defaultData = {
    name: faker.commerce.productName(),
    uuid: faker.string.uuid(),
    cardCount: faker.number.int({ min: 1, max: 100 }),
  };

  const data = { ...defaultData, ...overrides };

  return new CardList({
    name: data.name,
    uuid: data.uuid,
    cardCount: data.cardCount,
  });
};

// Helper function to test card list option and its rendered output
interface TestCardListOptionProps {
  cardList: CardList;
  shouldHaveIcon?: boolean;
}

const testCardListOption = ({ cardList, shouldHaveIcon = false }: TestCardListOptionProps) => {
  const option = new CardListOption(cardList);

  // Test constructor properties
  expect(option.label).toBe(cardList.get('name'));
  expect(option.value).toBe(cardList.get('uuid'));
  expect(option.cardCount).toBe(cardList.get('cardCount'));
  expect(option.icon).toBeUndefined();

  // Test formatted option label
  if (shouldHaveIcon) {
    option.icon = true;
  }

  const formattedOption = CardListOption.formatOptionLabel(option);
  const { container } = render(formattedOption);

  // Test rendered content
  expect(container.textContent).toContain(option.label);

  const expectedCardText = option.cardCount === 1 ? '1 card' : `${option.cardCount} cards`;
  expect(container.textContent).toContain(expectedCardText);

  const iconElement = container.querySelector('svg');
  if (shouldHaveIcon) {
    expect(iconElement).toBeInTheDocument();
  } else {
    expect(iconElement).not.toBeInTheDocument();
  }

  return { option, container };
};

describe('CardListOption', () => {
  describe('when CardList', () => {
    it('creates option and renders without icon', () => {
      const cardList = createMockCardList();
      testCardListOption({ cardList, shouldHaveIcon: false });
    });

    it('creates option and renders with icon', () => {
      const cardList = createMockCardList();
      testCardListOption({ cardList, shouldHaveIcon: true });
    });
  });

  describe('with different card counts', () => {
    it('handles single card', () => {
      const cardList = createMockCardList({ cardCount: 1 });
      const { container } = testCardListOption({ cardList });

      expect(container.textContent).toContain('1 card');
      expect(container.textContent).not.toContain('1 cards');
    });

    it('handles multiple cards', () => {
      const cardList = createMockCardList({ cardCount: 5 });
      const { container } = testCardListOption({ cardList });

      expect(container.textContent).toContain('5 cards');
    });

    it('handles zero cards', () => {
      const cardList = createMockCardList({ cardCount: 0 });
      const { container } = testCardListOption({ cardList });

      expect(container.textContent).toContain('0 cards');
    });
  });
});

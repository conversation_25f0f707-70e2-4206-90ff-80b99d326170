import { fireEvent, render, screen } from '@testing-library/react';
import React, { ComponentProps } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { Checkbox } from './Checkbox';

const mockOnChange = vi.fn();

const DEFAULT_PROPS: ComponentProps<typeof Checkbox> = {
  onChange: mockOnChange,
};

const renderCheckbox = (props: Partial<ComponentProps<typeof Checkbox>> = {}) => {
  return render(<Checkbox {...DEFAULT_PROPS} {...props} />);
};

describe('Checkbox', () => {
  describe('when rendered', () => {
    it('renders checkbox container', () => {
      renderCheckbox();
      expect(screen.getByRole('checkbox')).toBeInTheDocument();
      expect(screen.getByRole('checkbox').parentElement).toHaveClass('checkbox');
    });

    it('renders hidden input element', () => {
      renderCheckbox();
      expect(screen.getByRole('checkbox', { hidden: true })).toBeInTheDocument();
    });

    it('applies custom className when provided', () => {
      renderCheckbox({ className: 'custom-checkbox' });
      expect(screen.getByRole('checkbox').parentElement).toHaveClass('checkbox', 'custom-checkbox');
    });
  });

  describe('visual states', () => {
    describe('unchecked state', () => {
      it('does not have is-checked class when unchecked', () => {
        renderCheckbox({ checked: false });
        expect(screen.getByRole('checkbox').parentElement).not.toHaveClass('is-checked');
      });

      it('does not render any icon when unchecked', () => {
        renderCheckbox({ checked: false });
        expect(screen.queryByRole('img', { hidden: true })).not.toBeInTheDocument();
      });
    });

    describe('checked state', () => {
      it('has is-checked class when checked', () => {
        renderCheckbox({ checked: true });
        expect(screen.getByRole('checkbox').parentElement).toHaveClass('is-checked');
      });

      it('renders check icon when checked', () => {
        renderCheckbox({ checked: true });
        const icon = screen.getByRole('img', { hidden: true });
        expect(icon).toBeInTheDocument();
        expect(icon).toHaveAttribute('width', '18px');
        expect(icon).toHaveAttribute('height', '18px');
      });
    });

    describe('multiple state', () => {
      it('has is-checked class when multiple is true', () => {
        renderCheckbox({ multiple: true });
        expect(screen.getByRole('checkbox').parentElement).toHaveClass('is-checked');
      });

      it('renders remove icon when multiple is true and checked is falsy', () => {
        renderCheckbox({ multiple: true });
        const icon = screen.getByRole('img', { hidden: true });
        expect(icon).toBeInTheDocument();
        expect(icon).toHaveAttribute('width', '18px');
        expect(icon).toHaveAttribute('height', '18px');
      });
    });

    describe('focused state', () => {
      it('does not have is-focused class by default', () => {
        renderCheckbox();
        expect(screen.getByRole('checkbox').parentElement).not.toHaveClass('is-focused');
      });
    });
  });

  describe('disabled state', () => {
    it('disables the input element when disabled', () => {
      renderCheckbox({ disabled: true });
      const input = screen.getByRole('checkbox');
      expect(input).toBeDisabled();
    });

    it('does not call onChange when clicked while disabled', () => {
      renderCheckbox({ disabled: true });
      const checkbox = screen.getByRole('checkbox').parentElement;

      fireEvent.click(checkbox!);
      expect(mockOnChange).not.toHaveBeenCalled();
    });

    it('does not have cursor style when not disabled', () => {
      renderCheckbox({ disabled: false });
      const checkbox = screen.getByRole('checkbox').parentElement;
      expect(checkbox).not.toHaveStyle({ cursor: 'default' });
    });
  });

  describe('controlled mode', () => {
    it('uses checked prop value when provided', () => {
      renderCheckbox({ checked: true });
      const checkbox = screen.getByRole('checkbox').parentElement;
      expect(checkbox).toHaveClass('is-checked');
    });

    it('calls onChange with opposite value when clicked', () => {
      renderCheckbox({ checked: false });
      const checkbox = screen.getByRole('checkbox').parentElement;

      fireEvent.click(checkbox!);
      expect(mockOnChange).toHaveBeenCalledWith(true);
    });

    it('calls onChange with false when checked is true and clicked', () => {
      renderCheckbox({ checked: true });
      const checkbox = screen.getByRole('checkbox').parentElement;

      fireEvent.click(checkbox!);
      expect(mockOnChange).toHaveBeenCalledWith(false);
    });

    it('does not change internal state when controlled', () => {
      const { rerender } = renderCheckbox({ checked: false });
      const checkbox = screen.getByRole('checkbox').parentElement;

      fireEvent.click(checkbox!);

      // Re-render with same props - should still be unchecked
      rerender(<Checkbox checked={false} onChange={mockOnChange} />);
      expect(checkbox).not.toHaveClass('is-checked');
    });
  });

  describe('uncontrolled mode', () => {
    it('toggles internal state when clicked and no onChange provided', () => {
      renderCheckbox({ onChange: undefined });
      const checkbox = screen.getByRole('checkbox').parentElement;
      expect(checkbox).not.toHaveClass('is-checked');

      fireEvent.click(checkbox!);
      expect(checkbox).toHaveClass('is-checked');

      fireEvent.click(checkbox!);
      expect(checkbox).not.toHaveClass('is-checked');
    });

    it('calls onChange and does not change internal state when onChange is provided', () => {
      renderCheckbox();
      const checkbox = screen.getByRole('checkbox').parentElement;

      fireEvent.click(checkbox!);
      expect(mockOnChange).toHaveBeenCalledWith(true);
      // Internal state should not change when onChange is provided
      expect(checkbox).not.toHaveClass('is-checked');
    });
  });
});

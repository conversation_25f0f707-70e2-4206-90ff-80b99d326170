import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, vi } from 'vitest';
import { createFakePokeSet } from '../../../../tests/fake/FakeCardSet';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { FilterComponentType } from '../../../models/FilterComponent';
import { CardSetFilter } from '../../../models/filters/CardSetFilter';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { PokeFilter } from '../../../models/filters/pokemon/PokeFilters';
import { PokeFilterComponent } from './PokeFilterComponent';

type PokeFilterComponentProps = React.ComponentProps<typeof PokeFilterComponent>;

const DEFAULT_PROPS: Omit<PokeFilterComponentProps, 'dispatcher'> = {
  filter: new PokeFilter(),
  type: FilterComponentType.QUERY,
  displayType: FilterDisplay.PERMANENT,
  onUpdate: vi.fn(),
  disabled: false,
};

const renderPokeFilterComponent = (props: Partial<PokeFilterComponentProps> = {}) => {
  return renderWithDispatcher(PokeFilterComponent, { ...DEFAULT_PROPS, ...props });
};

describe('PokeFilterComponent', () => {
  describe('FilterComponentType', () => {
    describe('when type prop is QUERY', () => {
      it('renders QueryFilter ', () => {
        renderPokeFilterComponent();
        expect(screen.getByText('Card Name')).toBeInTheDocument();
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
      test('triggers onUpdate when the input value changes', () => {
        renderPokeFilterComponent();
        fireEvent.change(screen.getByRole('textbox'), { target: { value: 'test query' } });
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      test('triggers onUpdate when clear button clicked', () => {
        renderPokeFilterComponent();
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      it('uses disabled prop', () => {
        renderPokeFilterComponent({ disabled: true });
        expect(screen.getByRole('textbox')).toBeDisabled();
      });
    });
    describe('when type prop is PRICE', () => {
      it('renders PriceMinMaxFilters ', () => {
        renderPokeFilterComponent({
          type: FilterComponentType.PRICE,
        });
        expect(screen.getByText('Price Range')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Min Price')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Max Price')).toBeInTheDocument();
      });
      test('triggers onUpdate when the input value changes', () => {
        renderPokeFilterComponent({
          type: FilterComponentType.PRICE,
        });
        fireEvent.change(screen.getByPlaceholderText('Min Price'), { target: { value: '10000' } });
        fireEvent.change(screen.getByPlaceholderText('Max Price'), { target: { value: '12000' } });
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalledTimes(2);
      });
      test('triggers onUpdate when clear button clicked', () => {
        renderPokeFilterComponent({
          type: FilterComponentType.PRICE,
        });
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      it('uses disabled prop', () => {
        renderPokeFilterComponent({ type: FilterComponentType.PRICE, disabled: true });
        expect(screen.getByPlaceholderText('Min Price')).toBeDisabled();
        expect(screen.getByPlaceholderText('Max Price')).toBeDisabled();

        // Testing click behavior rather than .toBeDisabled() because this component
        // doesn't use a <button> element, so we verify the disabled functionality
        // by ensuring the onClick handler is not triggered when disabled
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).not.toHaveBeenCalled();
      });
    });

    describe('when type prop is STARTS_WITH', () => {
      it('renders StartsWithFilter with title prop is "Starts With"', () => {
        renderPokeFilterComponent({
          type: FilterComponentType.STARTS_WITH,
        });
        expect(screen.getByText('Starts With')).toBeInTheDocument();
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
      test('triggers onUpdate when the input value changes', () => {
        renderPokeFilterComponent({
          type: FilterComponentType.STARTS_WITH,
        });
        fireEvent.change(screen.getByRole('textbox'), { target: { value: 's:' } });
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      test('triggers onUpdate when clear button clicked', () => {
        renderPokeFilterComponent({
          type: FilterComponentType.STARTS_WITH,
        });
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      it('uses disabled prop', () => {
        renderPokeFilterComponent({ type: FilterComponentType.STARTS_WITH, disabled: true });
        expect(screen.getByRole('textbox')).toBeDisabled();

        // Testing click behavior rather than .toBeDisabled() because this component
        // doesn't use a <button> element, so we verify the disabled functionality
        // by ensuring the onClick handler is not triggered when disabled
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).not.toHaveBeenCalled();
      });
    });
    describe('when type prop is SET_NAME_STARTS_WITH', () => {
      it('renders StartsWithFilter with title prop is "Set Name Starts With"', () => {
        renderPokeFilterComponent({
          type: FilterComponentType.SET_NAME_STARTS_WITH,
        });
        expect(screen.getByText('Set Name Starts With')).toBeInTheDocument();
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
      test('triggers onUpdate when the input value changes', () => {
        renderPokeFilterComponent({
          type: FilterComponentType.SET_NAME_STARTS_WITH,
        });
        fireEvent.change(screen.getByRole('textbox'), { target: { value: 's:' } });
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      test('triggers s onUpdate when clear button clicked', () => {
        renderPokeFilterComponent({
          type: FilterComponentType.SET_NAME_STARTS_WITH,
        });
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      it('uses disabled prop', () => {
        renderPokeFilterComponent({ type: FilterComponentType.SET_NAME_STARTS_WITH, disabled: true });
        expect(screen.getByRole('textbox')).toBeDisabled();

        // Testing click behavior rather than .toBeDisabled() because this component
        // doesn't use a <button> element, so we verify the disabled functionality
        // by ensuring the onClick handler is not triggered when disabled
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).not.toHaveBeenCalled();
      });
    });
    describe('when type prop is CARD_SET', () => {
      it('renders PokeSetFilter ', () => {
        renderPokeFilterComponent({
          type: FilterComponentType.CARD_SET,
        });
        expect(screen.getByText('Set')).toBeInTheDocument();
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
      test('triggers onUpdate when filter changes', () => {
        const mockPokesSetFilter = createFakePokeSet(1).map((pokeSet) => [
          pokeSet.get('name'),
          new CardSetFilter({ include: true, cardSet: pokeSet }),
        ]);
        renderPokeFilterComponent({
          type: FilterComponentType.CARD_SET,
          filter: new PokeFilter({
            pokeSet: Immutable.OrderedMap<string, CardSetFilter>(mockPokesSetFilter),
          }),
        });
        const selectedElement = screen.getByText(mockPokesSetFilter[0][0] as string);
        fireEvent.click(selectedElement);
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      test('triggers onUpdate when clear button clicked', () => {
        renderPokeFilterComponent({
          type: FilterComponentType.CARD_SET,
        });
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).toHaveBeenCalled();
      });
      it('uses disabled prop', () => {
        renderPokeFilterComponent({ type: FilterComponentType.CARD_SET, disabled: true });
        expect(screen.getByRole('textbox')).toBeDisabled();

        // Testing click behavior rather than .toBeDisabled() because this component
        // doesn't use a <button> element, so we verify the disabled functionality
        // by ensuring the onClick handler is not triggered when disabled
        fireEvent.click(screen.getByText('Clear'));
        expect(DEFAULT_PROPS.onUpdate).not.toHaveBeenCalled();
      });
    });
  });
});

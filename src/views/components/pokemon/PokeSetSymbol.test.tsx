import { fireEvent, render, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it } from 'vitest';
import { PokeRarity } from '../../../models/pokemon/PokeRarity';
import { SetSymbolSize } from '../SetSymbol';
import { PokeSetSymbol } from './PokeSetSymbol';
type PokeSetSymbolProps = React.ComponentProps<typeof PokeSetSymbol>;

const DEFAULT_PROPS: PokeSetSymbolProps = {
  setName: 'Test SetName',
  setUUID: 'test-uuid',
  hoverText: true,
  collectorNumber: '123546',
  rarity: PokeRarity.RARE,
  size: SetSymbolSize.XS,
};

const renderPokeSetSymbol = (props: Partial<PokeSetSymbolProps> = {}) => {
  return render(<PokeSetSymbol {...DEFAULT_PROPS} {...props} />);
};

const showTooltip = (container: HTMLElement) => {
  const symbolContainer = container.querySelector(`.flex.set-symbol__${DEFAULT_PROPS.size}`) as HTMLElement;
  fireEvent.mouseEnter(symbolContainer);
  return symbolContainer;
};

describe('PokeSetSymbol', () => {
  describe('component render', () => {
    it('renders image based on setUUID', () => {
      renderPokeSetSymbol();
      const img = screen.getByRole<HTMLImageElement>('img');
      expect(img.src).toContain(DEFAULT_PROPS.setUUID?.[0]);
      expect(img.src).toContain(DEFAULT_PROPS.setUUID?.[1]);
      expect(img.src).toContain(`${DEFAULT_PROPS.setUUID}.png`);
    });
    it('renders correct class based on size', () => {
      const { container } = renderPokeSetSymbol({ size: SetSymbolSize.XL });
      expect(container.querySelector(`.flex.set-symbol__${SetSymbolSize.XL}`)).toBeInTheDocument();
    });
    test('defaults to XS size when size prop is undefined', () => {
      const { container } = renderPokeSetSymbol({ size: undefined });
      expect(container.querySelector('.flex.set-symbol__xs')).toBeInTheDocument();
    });
  });

  describe('hover text', () => {
    it('does not show when hoverText is false', () => {
      const { container } = renderPokeSetSymbol({ hoverText: false });
      showTooltip(container);
      const tooltip = container.querySelector('.set-symbol-tooltip-container');
      expect(tooltip).not.toBeInTheDocument();
    });
    it('shows on mouse enter', () => {
      const { container } = renderPokeSetSymbol();
      showTooltip(container);
      const tooltipContent = container.querySelector('.set-symbol-tooltip');
      expect(tooltipContent).toHaveTextContent(DEFAULT_PROPS.setName);
      expect(tooltipContent).toHaveTextContent(`${DEFAULT_PROPS.rarity} - ${DEFAULT_PROPS.collectorNumber}`);
    });
    it('hides on mouse leave', () => {
      const { container } = renderPokeSetSymbol();
      const symbolContainer = showTooltip(container);
      fireEvent.mouseLeave(symbolContainer);
      const tooltip = container.querySelector('.set-symbol-tooltip-container');
      expect(tooltip).not.toBeInTheDocument();
    });
  });
});

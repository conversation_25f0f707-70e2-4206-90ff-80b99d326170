import { fireEvent, screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck, FakeDeckBoard } from '../../../../tests/fake/FakeCardData';
import { FakeUser } from '../../../../tests/fake/FakeUserData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { Arranging } from '../../../models/Arranging';
import { Grouping } from '../../../models/Grouping';
import { DeckBuilderBoard } from './DeckBuilderBoard';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockRemoveDeckBoard = vi.mocked(DeckActions.removeDeckBoard);
const mockUpdateDeckBoard = vi.mocked(DeckActions.updateDeckBoard);

// Mock track helper
vi.mock('../../../helpers/ga_helper', () => ({
  default: vi.fn(),
}));

// Mock DeckLinkerWrapper
vi.mock('./DeckLinkerWrapper', () => ({
  DeckLinkerWrapper: ({
    children,
    onDismiss,
    isOpen,
  }: {
    children: React.ReactNode;
    onDismiss: () => void;
    isOpen: boolean;
  }) =>
    isOpen ? (
      <div data-testid="deck-linker-wrapper">
        {children}
        <button onClick={onDismiss}>Close Linker</button>
      </div>
    ) : null,
}));

type DeckBuilderBoardProps = React.ComponentProps<typeof DeckBuilderBoard>;

// Create a simple deck without cards for basic tests
const createEmptyDeck = () => {
  const mainBoard = create(FakeDeckBoard, { id: 1, name: 'Main' });
  return create(FakeDeck, {
    boards: Immutable.List([mainBoard]),
    deckCards: Immutable.Map(),
    cards: Immutable.Map(),
  });
};

const defaultProps: Omit<DeckBuilderBoardProps, 'dispatcher' | 'me'> = {
  deck: createEmptyDeck(),
  deckBoard: create(FakeDeckBoard, { id: 1, name: 'Main' }),
  deckArranging: Arranging.CARD_TYPE,
  deckGrouping: Grouping.NONE,
  mutable: true,
  linkable: true,
};

const renderDeckBuilderBoard = (props: Partial<DeckBuilderBoardProps> = {}) => {
  return renderWithDispatcher(DeckBuilderBoard, {
    ...defaultProps,
    me: create(FakeUser),
    ...props,
  });
};

describe('DeckBuilderBoard', () => {
  it('displays the board container', () => {
    const { container } = renderDeckBuilderBoard();
    expect(container.querySelector('.deckbuilder-board')).toBeInTheDocument();
  });

  it('displays the board title with card count', () => {
    renderDeckBuilderBoard();
    expect(screen.getByText('Main Board (0)')).toBeInTheDocument();
  });

  it('displays action buttons for custom boards when mutable', () => {
    const customBoard = create(FakeDeckBoard, { id: 2, name: 'Custom' });
    renderDeckBuilderBoard({ deckBoard: customBoard, mutable: true });
    expect(screen.getByText('Remove')).toBeInTheDocument();
    expect(screen.getByText('Rename')).toBeInTheDocument();
    expect(screen.getByText('Hide')).toBeInTheDocument();
  });

  it('hides remove and rename buttons for special boards', () => {
    const mainBoard = create(FakeDeckBoard, { id: 1, name: 'Main' });
    renderDeckBuilderBoard({ deckBoard: mainBoard, mutable: true });
    expect(screen.queryByText('Remove')).not.toBeInTheDocument();
    expect(screen.queryByText('Rename')).not.toBeInTheDocument();
  });

  it('does not display action buttons when not mutable', () => {
    renderDeckBuilderBoard({ mutable: false });
    expect(screen.queryByText('Remove')).not.toBeInTheDocument();
    expect(screen.queryByText('Rename')).not.toBeInTheDocument();
  });

  it('toggles hide/view button text', () => {
    renderDeckBuilderBoard();
    fireEvent.click(screen.getByText('Hide'));
    expect(screen.getByText('View')).toBeInTheDocument();
  });

  it('hides board content when hidden', () => {
    renderDeckBuilderBoard();
    fireEvent.click(screen.getByText('Hide'));
    expect(screen.queryByRole('heading', { level: 2 })).not.toBeInTheDocument();
  });

  it('shows confirmation dialog when remove is clicked', () => {
    const customBoard = create(FakeDeckBoard, { id: 2, name: 'Custom' });
    renderDeckBuilderBoard({ deckBoard: customBoard });
    fireEvent.click(screen.getByText('Remove'));
    expect(screen.getByText('Are you sure?')).toBeInTheDocument();
  });

  it('calls removeDeckBoard action when confirmed', () => {
    const customBoard = create(FakeDeckBoard, { id: 2, name: 'Custom' });
    const { dispatcher } = renderDeckBuilderBoard({ deckBoard: customBoard });
    fireEvent.click(screen.getByText('Remove'));
    fireEvent.click(screen.getByRole('button', { name: 'Remove' }));
    expect(mockRemoveDeckBoard).toHaveBeenCalledWith(defaultProps.deck, customBoard, dispatcher);
  });

  it('shows input field when rename is clicked', () => {
    const customBoard = create(FakeDeckBoard, { id: 2, name: 'Custom' });
    renderDeckBuilderBoard({ deckBoard: customBoard });
    fireEvent.click(screen.getByText('Rename'));
    expect(screen.getByDisplayValue('Custom')).toBeInTheDocument();
  });

  it('updates board name when input changes', () => {
    const customBoard = create(FakeDeckBoard, { id: 2, name: 'Custom' });
    renderDeckBuilderBoard({ deckBoard: customBoard });
    fireEvent.click(screen.getByText('Rename'));
    const input = screen.getByDisplayValue('Custom');
    fireEvent.change(input, { target: { value: 'New Name' } });
    expect(input).toHaveValue('New Name');
  });

  it('reverts name on blur without submit', () => {
    const customBoard = create(FakeDeckBoard, { id: 2, name: 'Custom' });
    renderDeckBuilderBoard({ deckBoard: customBoard });
    fireEvent.click(screen.getByText('Rename'));
    const input = screen.getByDisplayValue('Custom');
    fireEvent.change(input, { target: { value: 'New Name' } });
    fireEvent.blur(input);
    expect(screen.getByText('Custom Board (0)')).toBeInTheDocument();
  });

  it('submits name change on form submit', () => {
    const customBoard = create(FakeDeckBoard, { id: 2, name: 'Custom' });
    const deck = createEmptyDeck();
    const { dispatcher } = renderDeckBuilderBoard({ deckBoard: customBoard, deck });
    fireEvent.click(screen.getByText('Rename'));
    const input = screen.getByDisplayValue('Custom');
    fireEvent.change(input, { target: { value: 'New Name' } });
    const form = input.closest('form')!;
    fireEvent.submit(form);
    expect(mockUpdateDeckBoard).toHaveBeenCalledWith(deck, expect.objectContaining({ name: 'New Name' }), dispatcher);
  });

  it('renders with NAME grouping', () => {
    const { container } = renderDeckBuilderBoard({ deckGrouping: Grouping.NAME });
    expect(container.querySelector('.deckbuilder-board')).toBeInTheDocument();
  });

  it('renders with MANA_COST arrangement', () => {
    const { container } = renderDeckBuilderBoard({ deckArranging: Arranging.MANA_COST });
    expect(container.querySelector('.deckbuilder-board')).toBeInTheDocument();
  });

  it('renders with CARD_TYPE arrangement', () => {
    const { container } = renderDeckBuilderBoard({ deckArranging: Arranging.CARD_TYPE });
    expect(container.querySelector('.deckbuilder-board')).toBeInTheDocument();
  });

  it('updates state when props change', () => {
    const initialBoard = create(FakeDeckBoard, { id: 1, name: 'Initial' });
    const { rerenderWithDispatcher } = renderDeckBuilderBoard({ deckBoard: initialBoard });
    const newBoard = create(FakeDeckBoard, { id: 1, name: 'Updated' });
    rerenderWithDispatcher({ deckBoard: newBoard, me: create(FakeUser) });
    expect(screen.getByText('Updated Board (0)')).toBeInTheDocument();
  });

  it('initializes with correct state', () => {
    renderDeckBuilderBoard();
    expect(screen.getByText('Hide')).toBeInTheDocument();
    expect(screen.queryByText('Are you sure?')).not.toBeInTheDocument();
  });

  it('renders with linkable prop', () => {
    const { container } = renderDeckBuilderBoard({ linkable: true });
    expect(container.querySelector('.deckbuilder-board')).toBeInTheDocument();
  });

  it('does not show DeckLinkerWrapper initially', () => {
    renderDeckBuilderBoard({ linkable: true });
    expect(screen.queryByTestId('deck-linker-wrapper')).not.toBeInTheDocument();
  });
});

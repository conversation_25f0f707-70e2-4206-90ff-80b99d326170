import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { ComponentProps } from 'react';
import { describe, expect, it, test, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeCardList } from '../../../../tests/fake/FakeCardList';
import * as CardListAPI from '../../../api/CardList';
import { CardList } from '../../../models/CardList';
import { FilterDisplay } from '../../../models/filters/FilterDisplay';
import { CardListOption } from '../../components/CardListSuggestionItem';
import { CardListDropdown } from './CardListDropdown';

// Mock the CardList API
vi.mock('../../../api/CardList', () => ({
  all: vi.fn(),
}));

const mockOnSelectCardList = vi.fn();
const mockOnClear = vi.fn();

const DEFAULT_PROPS = {
  onSelectCardList: mockOnSelectCardList,
  onClear: mockOnClear,
  displayType: FilterDisplay.REMOVEABLE,
};

const renderCardListDropdown = (props: Partial<ComponentProps<typeof CardListDropdown>> = {}) => {
  return render(<CardListDropdown {...DEFAULT_PROPS} {...props} />);
};

// Helper functions
const openDropdown = () => {
  const selectContainer = screen.getByRole('textbox');
  fireEvent.mouseDown(selectContainer);
};

const mockCardListAPI = (cardLists: CardList[] = []) => {
  (CardListAPI.all as any).mockResolvedValue(Immutable.List(cardLists));
};

describe('CardListDropdown', () => {
  beforeEach(() => {
    mockCardListAPI();
  });

  describe('when rendered', () => {
    it('shows loading state', () => {
      renderCardListDropdown();

      expect(screen.getByRole('textbox')).toBeInTheDocument();
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('loads card lists', () => {
      renderCardListDropdown();

      expect(CardListAPI.all).toHaveBeenCalledWith('name', 'asc');
    });

    describe('with selected card list', () => {
      it('displays selected value', () => {
        const cardList = create(FakeCardList);

        renderCardListDropdown({ cardList });

        expect(screen.getByText(cardList.get('name'))).toBeInTheDocument();
      });
    });

    describe('with FilterTitle', () => {
      it('renders title', () => {
        renderCardListDropdown();

        expect(screen.getByText('Card List')).toBeInTheDocument();
      });
    });
  });

  describe('with react-select', () => {
    test('displays options', async () => {
      const cardList = create(FakeCardList);
      mockCardListAPI([cardList]);

      renderCardListDropdown();

      await waitFor(() => {
        openDropdown();
      });
      expect(screen.getByText(cardList.get('name'))).toBeInTheDocument();
    });

    test('triggers onSelectCardList', async () => {
      const cardList = create(FakeCardList);
      mockCardListAPI([cardList]);

      renderCardListDropdown();

      await waitFor(() => {
        openDropdown();
      });
      fireEvent.click(screen.getByText(cardList.get('name')));

      expect(mockOnSelectCardList).toHaveBeenCalledWith(
        expect.objectContaining({
          name: cardList.get('name'),
          uuid: cardList.get('uuid'),
        }),
      );
    });

    describe('with additional options', () => {
      it('includes all options', async () => {
        const cardList = create(FakeCardList);
        const additionalCardList = create(FakeCardList);
        const additionalOption = new CardListOption(additionalCardList);

        mockCardListAPI([cardList]);

        renderCardListDropdown({ additionalOptions: [additionalOption] });

        await waitFor(() => {
          openDropdown();
        });
        expect(screen.getByText(cardList.get('name'))).toBeInTheDocument();
        expect(screen.getByText(additionalCardList.get('name'))).toBeInTheDocument();
      });
    });

    describe('with empty card lists', () => {
      it('handles empty response', async () => {
        mockCardListAPI([]);

        renderCardListDropdown();

        await waitFor(() => {
          openDropdown();
        });

        // Should not crash and should show empty dropdown
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
    });

    describe('with disabled prop', () => {
      it('disables select', () => {
        renderCardListDropdown({ disabled: true });

        expect(screen.getByRole('textbox')).toBeDisabled();
      });
    });
  });

  describe('with ClearFilterButton', () => {
    it('renders remove button', () => {
      renderCardListDropdown({ displayType: FilterDisplay.REMOVEABLE });
      expect(screen.getByText('Remove')).toBeInTheDocument();
    });

    it('renders clear button', () => {
      renderCardListDropdown({ displayType: FilterDisplay.PERMANENT });
      expect(screen.getByText('Clear')).toBeInTheDocument();
    });

    test('triggers onSelectCardList', () => {
      renderCardListDropdown();

      fireEvent.click(screen.getByText('Remove'));

      expect(mockOnSelectCardList).toHaveBeenCalledWith(undefined);
    });

    test('triggers onClear', () => {
      renderCardListDropdown();

      fireEvent.click(screen.getByText('Remove'));

      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });

    describe('with disabled prop', () => {
      it('disables button', () => {
        renderCardListDropdown({ disabled: true });

        const removeButton = screen.getByText('Remove');
        expect(removeButton.closest('div')).toHaveStyle('cursor: default');
      });
    });
  });

  describe('with menu portal', () => {
    it('accepts portal target', () => {
      const portalDiv = document.createElement('div');

      renderCardListDropdown({ menuPortal: portalDiv });

      // Component should render without errors
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });
});

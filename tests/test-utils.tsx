import { RenderOptions, RenderResult, render as rtlRender } from '@testing-library/react';
import * as React from 'react';
import { ActionType } from '../src/actions/Actions';
import { SubscriptionsPayload } from '../src/actions/SubscriptionActions';
import Dispatcher from '../src/dispatcher/Dispatcher';
import { PaymentSubscriptionType } from '../src/models/PaymentEnums';
import { Subscription, SubscriptionType } from '../src/models/Subscriptions';

export type RenderWithDispatcherResult<P> = RenderResult & {
  /**
   * Rerenders the component with new props
   */
  rerenderWithDispatcher: (newProps: Partial<Omit<P, 'dispatcher'>>) => void;
  /**
   * The dispatcher instance used
   */
  dispatcher: Dispatcher;
  /**
   * Sets up a merchant subscription for testing purposes
   *
   * This utility function creates and dispatches a merchant subscription action,
   * useful for testing components that depend on merchant subscription state.
   */
  setupMerchantSubscription: () => void;
};

/**
 * Renders a component with a real Dispatcher instance
 *
 * @param Component - React component that expects a dispatcher prop
 * @param props - Props to pass to the component (excluding dispatcher)
 * @param options - Additional render options for RTL
 * @returns React Testing Library render result with additional utilities:
 *   - `rerenderWithDispatcher`: Function to rerender with new props
 *   - `dispatcher`: The dispatcher instance used
 *   - `setupMerchantSubscription`: Utility to set up merchant subscription state
 *
 * @example
 * const { getByTestId } = renderWithDispatcher(
 *   CardComponent,
 *   { cardId: '456' },
 *   { wrapper: ThemeProvider }
 * );
 * expect(getByTestId('card-456')).toBeInTheDocument();
 *
 * @example
 * // Testing component behavior with merchant subscription
 * const { getByText, setupMerchantSubscription } = renderWithDispatcher(
 *   PremiumFeatureComponent,
 *   { featureName: 'Advanced Search' }
 * );
 *
 * setupMerchantSubscription();
 * expect(getByText('Advanced Search Enabled')).toBeInTheDocument();
 */
export function renderWithDispatcher<P extends { dispatcher: Dispatcher }>(
  Component: React.ComponentType<P>,
  props: Omit<P, 'dispatcher'> = {} as Omit<P, 'dispatcher'>,
  options: RenderOptions = {},
): RenderWithDispatcherResult<P> {
  // Create a new dispatcher for each render
  const dispatcher = new Dispatcher();

  // Render the component with the dispatcher
  const { rerender, ...restRender } = rtlRender(<Component {...(props as P)} dispatcher={dispatcher} />, options);
  const rerenderWithDispatcher: RenderWithDispatcherResult<P>['rerenderWithDispatcher'] = (newProps = {}) => {
    rerender(<Component {...(props as P)} {...newProps} dispatcher={dispatcher} />);
  };

  const setupMerchantSubscription = () => {
    dispatcher.dispatch({
      type: ActionType.SUBSCRIPTIONS,
      payload: new SubscriptionsPayload({
        subscription: new Subscription({
          type: SubscriptionType.MERCHANT,
          active: true,
          provider: PaymentSubscriptionType.STRIPE,
        }),
      }),
    });
  };

  return {
    ...restRender,
    rerender,
    rerenderWithDispatcher,
    dispatcher,
    setupMerchantSubscription,
  };
}

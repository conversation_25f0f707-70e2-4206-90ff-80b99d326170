import { faker } from '@faker-js/faker';
import * as Immutable from 'immutable';
import { Condition } from '../../src/models/Condition';
import { ColorFilter, ColorOption } from '../../src/models/filters/ColorFilter';
import { FilterState } from '../../src/models/filters/FilterState';
import { LorcanaFilter } from '../../src/models/filters/lorcana/LorcanaFilters';
import { MTGFilter } from '../../src/models/filters/mtg/MTGFilters';
import { PriceFilter } from '../../src/models/filters/PriceFilter';
import { SubtypeFilter } from '../../src/models/filters/SubtypeFilter';
import { SupertypeFilter } from '../../src/models/filters/SupertypeFilter';
import { TagFilter } from '../../src/models/filters/TagFilter';
import { TagFilterData } from '../../src/models/filters/TagFilterData';
import { TypeFilters, TypeOption } from '../../src/models/filters/TypeFilters';
import { Legality } from '../../src/models/Legality';
import { PricingSource } from '../../src/models/PricingSource';
import { Rarity } from '../../src/models/Rarity';
import { Supertype } from '../../src/models/Supertype';
import { Tag } from '../../src/models/Tags';
import { create, createArray, FakeCardList, fakeCardSetFilterMap, fakeCardSetTypeFilterMap, FakeTag } from './Fake';
import { IFake } from './FakeInterface';

export class FakeMTGFilter implements IFake<MTGFilter> {
  /**
   * Generates a Filter that contains a valid entry for every filter type, which may not always be desriable.
   * TODO: If needed, refactor to allow for generating filters that do not nessecarily contain each filter type.
   * */
  public fake(options?: { empty: boolean }) {
    if (options && options.empty) return new MTGFilter();

    return new MTGFilter({
      page: faker.number.int({ min: 1 }),
      pageSize: faker.number.int({ min: 1 }),
      query: faker.lorem.lines(1),
      color: create(FakeColorFilter).set(faker.helpers.enumValue(ColorOption), FilterState.ON),
      cardTypes: create(FakeTypeFilters).set(faker.helpers.enumValue(TypeOption), FilterState.ON),
      rarities: generateNonEmptyRarityFilter(),
      foil: faker.datatype.boolean(),
      isReserved: faker.datatype.boolean(),
      tagFilter: new TagFilterData({
        tagFilter: fakeTagFilterMap(),
        excludeUnselected: faker.datatype.boolean(),
      }),
      setFilter: fakeCardSetFilterMap(),
      setTypes: fakeCardSetTypeFilterMap(),
      staged: faker.datatype.boolean(),
      priceFilter: create(FakePriceFilter),
      cardList: create(FakeCardList),
      condition: faker.helpers.enumValue(Condition),
      legality: faker.helpers.enumValue(Legality),
      supertypes: generateNonEmptySupertypesFilter(),
      subtypes: generateNonEmptySubtypesFilter(),
    });
  }
}

export class FakeColorFilter implements IFake<ColorFilter> {
  public fake() {
    return new ColorFilter({
      white: faker.helpers.enumValue(FilterState),
      blue: faker.helpers.enumValue(FilterState),
      black: faker.helpers.enumValue(FilterState),
      red: faker.helpers.enumValue(FilterState),
      green: faker.helpers.enumValue(FilterState),
      colorless: faker.helpers.enumValue(FilterState),
      multicolored: faker.helpers.enumValue(FilterState),
    });
  }
}

export class FakeTypeFilters implements IFake<TypeFilters> {
  public fake() {
    return new TypeFilters({
      creature: faker.helpers.enumValue(FilterState),
      planeswalker: faker.helpers.enumValue(FilterState),
      artifact: faker.helpers.enumValue(FilterState),
      enchantment: faker.helpers.enumValue(FilterState),
      land: faker.helpers.enumValue(FilterState),
      instant: faker.helpers.enumValue(FilterState),
      sorcery: faker.helpers.enumValue(FilterState),
      tribal: faker.helpers.enumValue(FilterState),
      conspiracy: faker.helpers.enumValue(FilterState),
      plane: faker.helpers.enumValue(FilterState),
      phenomenon: faker.helpers.enumValue(FilterState),
      vanguard: faker.helpers.enumValue(FilterState),
      scheme: faker.helpers.enumValue(FilterState),
      enchant: faker.helpers.enumValue(FilterState),
      player: faker.helpers.enumValue(FilterState),
      multitype: faker.helpers.enumValue(FilterState),
    });
  }
}

export class FakeRarity implements IFake<Rarity> {
  public fake() {
    return faker.helpers.enumValue(Rarity);
  }
}

export class FakeSupertype implements IFake<Supertype> {
  public fake() {
    return faker.helpers.enumValue(Supertype);
  }
}

export class FakeTagFilter implements IFake<TagFilter> {
  public fake() {
    return new TagFilter({
      include: faker.datatype.boolean(),
      tag: create(FakeTag),
    });
  }
}

export function fakeTagFilterMap(): Immutable.OrderedMap<string, TagFilter> {
  return Immutable.OrderedMap<string, TagFilter>(
    createArray(FakeTagFilter, faker.number.int({ max: 5 })).map((tag: Tag) => {
      return [tag.get('name'), tag];
    }),
  );
}

/**
 * Generates a PriceFilter that contains a valid entry for both min and max that are eqaul, which may not always be desriable.
 * TODO: If needed, refactor to allow for generating more variations of PriceFilter.
 * */
export class FakePriceFilter implements IFake<PriceFilter> {
  public fake() {
    const randomNumber = faker.number.int({ min: 0 });
    return new PriceFilter({
      max: randomNumber.toString(),
      min: randomNumber.toString(),
      source: faker.datatype.boolean() ? PricingSource.TCG_PLAYER : PricingSource.CARD_KINGDOM,
    });
  }
}

/** Guarenteed to generate a non-empty rarity filter as there are no circumstances where Fake.Rarity.fake() will
 * produce anything other than a valid Rarity. Even if all rarties are equal, the OrderedSet will simply simplify it to
 * an OrderedSet of size === 1 */
export function generateNonEmptyRarityFilter() {
  return Immutable.OrderedSet<Rarity>(createArray<Rarity>(FakeRarity, 6));
}

/** Guarenteed to generate a non-empty supertype filter as there are no circumstances where Fake.Rarity.fake() will
 * produce anything other than a valid SupertypeFilter. Even if all rarties are equal, the OrderedSet will simply simplify it to
 * an OrderedSet of size === 1 */
export function generateNonEmptySupertypesFilter() {
  return Immutable.OrderedMap<string, SupertypeFilter>(
    createArray(FakeSupertype, 6).map((value: Supertype) => {
      return [value, new SupertypeFilter({ include: true, supertype: value })];
    }),
  );
}

/** Limitation - Generating a random number of lorem impsum words (between 1-4, 4 is the largest number of subtypes on a creature)
 * for simplicity of testing instead of valid subtype. */
export function generateNonEmptySubtypesFilter() {
  const randomNumber = faker.number.int({ min: 1, max: 4 });
  const words: string[] = [];
  for (let i = 0; i < randomNumber; i++) {
    words.push(faker.lorem.word());
  }
  const TEST_VARIABLE = Immutable.OrderedMap<string, SubtypeFilter>(
    words.map((value: string | undefined) => {
      return [value, new SubtypeFilter({ include: true, subtype: value as string })];
    }),
  );
  return TEST_VARIABLE;
}

export class FakeLorcanaFilter implements IFake<LorcanaFilter> {
  public fake(options?: { empty: boolean }) {
    if (options && options.empty) return new LorcanaFilter();

    return new LorcanaFilter({
      page: faker.number.int({ min: 1 }),
      pageSize: faker.number.int({ min: 1 }),
      name: faker.lorem.lines(1),
      price: create(FakePriceFilter),
      startsWith: faker.string.alpha(1),
      setStartsWith: faker.string.alpha(1),
      staged: faker.datatype.boolean(),
      sessionUUID: faker.string.uuid(),
    });
  }
}
